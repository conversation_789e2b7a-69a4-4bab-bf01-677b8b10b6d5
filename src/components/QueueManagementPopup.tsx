import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  LinearProgress,
  Chip,
  Alert,
  Paper,
} from '@mui/material';
import {
  Close as CloseIcon,
  Cancel as CancelIcon,
  PlayArrow as RunningIcon,
  Schedule as PendingIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CompletedIcon,
  Error as ErrorIcon,
  Queue as QueueIcon,
} from '@mui/icons-material';
import { useAppDispatch, useAppSelector } from '@store';
import {
  selectImportQueue,
  selectCurrentTask,
  selectPendingTasks,
  selectCompletedTasks,
  selectQueueSummary,
  removeFromQueue,
} from '@store/slices/importQueueSlice';
import { ImportQueueTask, ImportQueueTaskStatus } from '@app-types';
import { importQueueService } from '@services/importQueue';

interface QueueManagementPopupProps {
  open: boolean;
  onClose: () => void;
}

const QueueManagementPopup: React.FC<QueueManagementPopupProps> = ({ open, onClose }) => {
  const dispatch = useAppDispatch();
  const queueState = useAppSelector(selectImportQueue);
  const currentTask = useAppSelector(selectCurrentTask);
  const pendingTasks = useAppSelector(selectPendingTasks);
  const completedTasks = useAppSelector(selectCompletedTasks);
  const queueSummary = useAppSelector(selectQueueSummary);

  const handleRemoveTask = async (taskId: string) => {
    try {
      await dispatch(removeFromQueue(taskId)).unwrap();
    } catch (error) {
      console.error('Failed to remove task:', error);
    }
  };

  const getTaskIcon = (status: ImportQueueTaskStatus) => {
    switch (status) {
      case ImportQueueTaskStatus.PENDING:
        return <PendingIcon color="action" />;
      case ImportQueueTaskStatus.RUNNING:
        return <RunningIcon color="primary" />;
      case ImportQueueTaskStatus.COMPLETED:
        return <CompletedIcon color="success" />;
      case ImportQueueTaskStatus.FAILED:
        return <ErrorIcon color="error" />;
      case ImportQueueTaskStatus.CANCELLED:
        return <CancelIcon color="action" />;
      default:
        return <QueueIcon />;
    }
  };

  const getStatusChip = (task: ImportQueueTask) => {
    const statusText = importQueueService.getStatusDisplayText(task);
    let color: 'default' | 'primary' | 'success' | 'error' | 'warning' = 'default';

    switch (task.status) {
      case ImportQueueTaskStatus.PENDING:
        color = 'default';
        break;
      case ImportQueueTaskStatus.RUNNING:
        color = 'primary';
        break;
      case ImportQueueTaskStatus.COMPLETED:
        color = 'success';
        break;
      case ImportQueueTaskStatus.FAILED:
        color = 'error';
        break;
      case ImportQueueTaskStatus.CANCELLED:
        color = 'warning';
        break;
    }

    return (
      <Chip
        label={statusText}
        color={color}
        size="small"
        variant="outlined"
        data-testid={`task-status-chip-${task.id}`}
      />
    );
  };

  const renderProgressBars = (task: ImportQueueTask) => {
    if (task.status !== ImportQueueTaskStatus.RUNNING) {
      return null;
    }

    const categoryProgress = importQueueService.getCategoryProgress(task);
    const recipeProgress = importQueueService.getRecipeProgress(task);
    const currentPhase = importQueueService.getCurrentPhaseDescription(task);

    // Always show progress bars for running tasks - simplified conditions
    const showCategoryProgress = task.progress && (
      categoryProgress.isActive ||
      task.progress.status === 'starting' ||
      task.progress.status === 'crawlingCategories' ||
      task.progress.status === 'extractingRecipes' ||
      task.progress.status === 'filteringExisting' ||
      categoryProgress.total > 0
    );

    const showRecipeProgress = task.progress && (
      recipeProgress.isActive ||
      task.progress.status === 'importingRecipes' ||
      recipeProgress.total > 0
    );

    // If no specific progress bars are shown, show a general progress indicator
    const showGeneralProgress = task.status === ImportQueueTaskStatus.RUNNING &&
      !showCategoryProgress && !showRecipeProgress;

    return (
      <Box sx={{ width: '100%', mt: 1 }}>
        {/* Current Phase Description */}
        {currentPhase && (
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
            {currentPhase}
          </Typography>
        )}

        {/* Category Scraping Progress */}
        {showCategoryProgress && (
          <Box sx={{ mb: 2.5 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" color="text.primary" sx={{ fontWeight: 500 }}>
                {task.progress?.status === 'starting' ? 'Initializing...' :
                 task.progress?.status === 'filteringExisting' ? 'Filtering existing recipes...' :
                 'Scraping categories'}
              </Typography>
              <Chip
                label={`${categoryProgress.current}/${categoryProgress.total || '?'}`}
                size="small"
                variant="outlined"
                color="primary"
                sx={{
                  fontWeight: 600,
                  fontSize: '0.75rem',
                  height: 24
                }}
              />
            </Box>
            <LinearProgress
              variant={categoryProgress.total > 0 ? "determinate" : "indeterminate"}
              value={categoryProgress.total > 0 ? categoryProgress.percentage : undefined}
              color={categoryProgress.isActive || task.progress?.status === 'starting' || task.progress?.status === 'filteringExisting' ? 'primary' : 'secondary'}
              data-testid={`category-progress-bar-${task.id}`}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 4,
                  transition: 'transform 0.4s ease-in-out'
                }
              }}
            />
          </Box>
        )}

        {/* Recipe Import Progress */}
        {showRecipeProgress && (
          <Box sx={{ mb: 2.5 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" color="text.primary" sx={{ fontWeight: 500 }}>
                Importing recipes
              </Typography>
              <Chip
                label={`${recipeProgress.current}/${recipeProgress.total || '?'}`}
                size="small"
                variant="outlined"
                color="primary"
                sx={{
                  fontWeight: 600,
                  fontSize: '0.75rem',
                  height: 24
                }}
              />
            </Box>
            <LinearProgress
              variant={recipeProgress.total > 0 ? "determinate" : "indeterminate"}
              value={recipeProgress.total > 0 ? recipeProgress.percentage : undefined}
              color={recipeProgress.isActive ? 'primary' : 'secondary'}
              data-testid={`recipe-progress-bar-${task.id}`}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 4,
                  transition: 'transform 0.4s ease-in-out'
                }
              }}
            />
          </Box>
        )}

        {/* General Progress Indicator - shown when no specific progress is available */}
        {showGeneralProgress && (
          <Box sx={{ mb: 2.5 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" color="text.primary" sx={{ fontWeight: 500 }}>
                Processing...
              </Typography>
              <Chip
                label="In progress"
                size="small"
                variant="filled"
                color="primary"
                sx={{
                  fontWeight: 600,
                  fontSize: '0.75rem',
                  height: 24
                }}
              />
            </Box>
            <LinearProgress
              variant="indeterminate"
              color="primary"
              data-testid={`general-progress-bar-${task.id}`}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 4,
                  animation: 'progress-indeterminate 2s linear infinite'
                }
              }}
            />
          </Box>
        )}

        {/* Overall Progress Summary */}
        {task.progress && (
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
            Overall: {importQueueService.getProgressPercentage(task)}% complete
          </Typography>
        )}

        {/* Debug Information - Remove this in production */}
        {process.env.NODE_ENV === 'development' && (
          <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="caption" sx={{ display: 'block', fontFamily: 'monospace' }}>
              Debug: Status={task.status}, Progress={task.progress ? JSON.stringify({
                status: task.progress.status,
                processedCategories: task.progress.processedCategories,
                totalCategories: task.progress.totalCategories,
                processedRecipes: task.progress.processedRecipes,
                totalRecipes: task.progress.totalRecipes
              }) : 'null'}
            </Typography>
          </Box>
        )}
      </Box>
    );
  };

  const renderTaskItem = (task: ImportQueueTask, isCurrentTask: boolean = false) => {
    const estimatedTime = importQueueService.getEstimatedTimeRemaining(task);
    const canRemove = importQueueService.canRemoveTask(task);

    return (
      <Paper
        key={task.id}
        data-testid={`queue-task-item-${task.id}`}
        elevation={isCurrentTask ? 2 : 0}
        sx={{
          border: isCurrentTask ? '2px solid' : '1px solid',
          borderColor: isCurrentTask ? 'primary.main' : 'divider',
          borderRadius: 2,
          mb: 2,
          backgroundColor: isCurrentTask ? 'primary.50' : 'background.paper',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            borderColor: isCurrentTask ? 'primary.main' : 'primary.light',
            boxShadow: isCurrentTask ? 2 : 1,
          },
          overflow: 'hidden'
        }}
      >
        <Box sx={{ p: 2.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
            {/* Status Icon */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 40,
              height: 40,
              borderRadius: '50%',
              backgroundColor: isCurrentTask ? 'primary.100' : 'grey.100',
              flexShrink: 0,
              mt: 0.5
            }}>
              {getTaskIcon(task.status)}
            </Box>

            {/* Task Content */}
            <Box sx={{ flex: 1, minWidth: 0 }}>
              {/* Task Header */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 1.5 }}>
                <Typography
                  variant="subtitle1"
                  component="h3"
                  sx={{
                    fontWeight: 600,
                    color: isCurrentTask ? 'primary.main' : 'text.primary',
                    flex: 1,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {task.description}
                </Typography>
                {isCurrentTask && (
                  <Chip
                    label="Current"
                    color="primary"
                    size="small"
                    variant="filled"
                    data-testid={`current-task-indicator-${task.id}`}
                    sx={{
                      fontWeight: 600,
                      fontSize: '0.75rem'
                    }}
                  />
                )}
              </Box>

              {/* Status and Time */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                {getStatusChip(task)}
                {estimatedTime && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <ScheduleIcon sx={{ fontSize: '1rem', color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                      {estimatedTime} remaining
                    </Typography>
                  </Box>
                )}
              </Box>

              {/* Progress Bars */}
              {renderProgressBars(task)}

              {/* Task Metadata */}
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{
                  display: 'block',
                  mt: 1.5,
                  fontWeight: 500
                }}
              >
                Added: {new Date(task.addedAt).toLocaleString()}
              </Typography>
            </Box>

            {/* Remove Button */}
            {canRemove && (
              <IconButton
                onClick={() => handleRemoveTask(task.id)}
                size="small"
                data-testid={`remove-task-button-${task.id}`}
                sx={{
                  color: 'error.main',
                  backgroundColor: 'error.50',
                  '&:hover': {
                    backgroundColor: 'error.100',
                    color: 'error.dark'
                  },
                  width: 36,
                  height: 36,
                  flexShrink: 0
                }}
              >
                <CancelIcon fontSize="small" />
              </IconButton>
            )}
          </Box>
        </Box>
      </Paper>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      data-testid="queue-management-popup"
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Import Queue Management</Typography>
          <IconButton onClick={onClose} size="small" data-testid="queue-popup-close-button">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        {queueState.error && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              borderRadius: 1.5,
              '& .MuiAlert-icon': {
                fontSize: '1.25rem'
              }
            }}
          >
            {queueState.error}
          </Alert>
        )}

        {/* Queue Summary */}
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 3,
            backgroundColor: 'grey.50',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 2
          }}
          data-testid="queue-summary"
        >
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 2 }}>
            Queue Summary
          </Typography>
          <Box sx={{ display: 'flex', gap: 1.5, flexWrap: 'wrap' }}>
            <Chip
              label={`${queueSummary.pending} Pending`}
              color={queueSummary.pending > 0 ? 'warning' : 'default'}
              variant={queueSummary.pending > 0 ? 'filled' : 'outlined'}
              size="medium"
              sx={{ fontWeight: 500 }}
            />
            <Chip
              label={`${queueState.isProcessing ? 1 : 0} Running`}
              color={queueState.isProcessing ? 'primary' : 'default'}
              variant={queueState.isProcessing ? 'filled' : 'outlined'}
              size="medium"
              sx={{ fontWeight: 500 }}
            />
            <Chip
              label={`${queueSummary.completed} Completed`}
              color={queueSummary.completed > 0 ? 'success' : 'default'}
              variant={queueSummary.completed > 0 ? 'filled' : 'outlined'}
              size="medium"
              sx={{ fontWeight: 500 }}
            />
            <Chip
              label={`${queueSummary.failed} Failed`}
              color={queueSummary.failed > 0 ? 'error' : 'default'}
              variant={queueSummary.failed > 0 ? 'filled' : 'outlined'}
              size="medium"
              sx={{ fontWeight: 500 }}
            />
          </Box>
        </Paper>

        {queueSummary.totalTasks === 0 ? (
          <Box sx={{
            textAlign: 'center',
            py: 6,
            px: 4,
            backgroundColor: 'grey.50',
            borderRadius: 2,
            border: '1px dashed',
            borderColor: 'divider'
          }} data-testid="empty-queue-message">
            <QueueIcon sx={{
              fontSize: 64,
              color: 'text.disabled',
              mb: 2,
              opacity: 0.5
            }} />
            <Typography variant="h6" color="text.secondary" sx={{ mb: 1, fontWeight: 600 }}>
              No import tasks in queue
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Add batch import tasks to see them here
            </Typography>
          </Box>
        ) : (
          <Box>
            {/* Current/Running Task */}
            {currentTask && (
              <Box sx={{ mb: 4 }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1.5,
                  mb: 2.5,
                  pb: 1,
                  borderBottom: '2px solid',
                  borderColor: 'primary.main'
                }}>
                  <RunningIcon color="primary" sx={{ fontSize: '1.5rem' }} />
                  <Typography
                    variant="h6"
                    data-testid="current-task-section"
                    sx={{
                      fontWeight: 700,
                      color: 'primary.main',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}
                  >
                    Current Task
                  </Typography>
                </Box>
                <Box>
                  {renderTaskItem(currentTask, true)}
                </Box>
              </Box>
            )}

            {/* Pending Tasks */}
            {pendingTasks.length > 0 && (
              <Box sx={{ mb: 4 }}>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1.5,
                  mb: 2.5,
                  pb: 1,
                  borderBottom: '2px solid',
                  borderColor: 'warning.main'
                }}>
                  <PendingIcon color="warning" sx={{ fontSize: '1.5rem' }} />
                  <Typography
                    variant="h6"
                    data-testid="pending-tasks-section"
                    sx={{
                      fontWeight: 700,
                      color: 'warning.main',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}
                  >
                    Pending Tasks ({pendingTasks.length})
                  </Typography>
                </Box>
                <Box>
                  {pendingTasks.map(task => renderTaskItem(task))}
                </Box>
              </Box>
            )}

            {/* Completed Tasks */}
            {completedTasks.length > 0 && (
              <Box>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1.5,
                  mb: 2.5,
                  pb: 1,
                  borderBottom: '2px solid',
                  borderColor: 'success.main'
                }}>
                  <CompletedIcon color="success" sx={{ fontSize: '1.5rem' }} />
                  <Typography
                    variant="h6"
                    data-testid="completed-tasks-section"
                    sx={{
                      fontWeight: 700,
                      color: 'success.main',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}
                  >
                    Completed Tasks ({completedTasks.length})
                  </Typography>
                </Box>
                <Box>
                  {completedTasks.slice(0, 5).map(task => renderTaskItem(task))}
                  {completedTasks.length > 5 && (
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        mt: 2,
                        backgroundColor: 'grey.50',
                        border: '1px solid',
                        borderColor: 'divider',
                        borderRadius: 2
                      }}
                    >
                      <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                        ... and {completedTasks.length - 5} more completed tasks
                      </Typography>
                    </Paper>
                  )}
                </Box>
              </Box>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{
        px: 3,
        py: 2,
        backgroundColor: 'grey.50',
        borderTop: '1px solid',
        borderColor: 'divider'
      }}>
        <Button
          onClick={onClose}
          data-testid="queue-popup-close-action-button"
          variant="contained"
          size="large"
          sx={{
            minWidth: 120,
            fontWeight: 600,
            textTransform: 'none',
            borderRadius: 1.5
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default QueueManagementPopup;
